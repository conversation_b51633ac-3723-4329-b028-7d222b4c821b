package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/sirupsen/logrus"

	"react-log-agent/pkg/agent"
	"react-log-agent/pkg/analyzer"
	"react-log-agent/pkg/config"
	"react-log-agent/pkg/mcp"
	"react-log-agent/pkg/monitor"
	"react-log-agent/pkg/rules"
)

var (
	configFile = flag.String("config", "configs/config.yaml", "Configuration file path")
	verbose    = flag.Bool("verbose", false, "Enable verbose logging")
	version    = flag.Bool("version", false, "Show version information")
)

const (
	AppName    = "ReAct Log Agent"
	AppVersion = "1.0.0"
)

func main() {
	flag.Parse()

	if *version {
		fmt.Printf("%s v%s\n", AppName, AppVersion)
		return
	}

	// 设置日志
	logger := setupLogger(*verbose)
	logger.Infof("Starting %s v%s", AppName, AppVersion)

	// 加载配置
	cfg, err := config.LoadConfig(*configFile)
	if err != nil {
		logger.Fatalf("Failed to load config: %v", err)
	}

	if err := cfg.Validate(); err != nil {
		logger.Fatalf("Invalid config: %v", err)
	}

	logger.Info("Configuration loaded successfully")

	// 创建上下文
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 初始化组件
	app, err := initializeApp(ctx, cfg, logger)
	if err != nil {
		logger.Fatalf("Failed to initialize application: %v", err)
	}

	// 启动应用
	if err := app.Start(ctx); err != nil {
		logger.Fatalf("Failed to start application: %v", err)
	}

	// 等待信号
	waitForSignal(logger, func() {
		logger.Info("Shutting down...")
		cancel()
		app.Stop()
	})

	logger.Info("Application stopped")
}

// App 应用结构
type App struct {
	config      *config.Config
	logger      *logrus.Logger
	monitor     *monitor.LogMonitor
	agent       *agent.ReActAgent
	mcpClient   *mcp.MCPClient
	ruleEngine  *rules.RuleEngine
	analyzer    *analyzer.LogAnalyzer
}

// initializeApp 初始化应用
func initializeApp(ctx context.Context, cfg *config.Config, logger *logrus.Logger) (*App, error) {
	app := &App{
		config: cfg,
		logger: logger,
	}

	// 初始化 LLM 客户端
	llmClient, err := agent.NewEinoLLMClient(ctx, &cfg.LLM)
	if err != nil {
		return nil, fmt.Errorf("failed to create LLM client: %w", err)
	}

	// 初始化 MCP 客户端
	app.mcpClient = mcp.NewMCPClient(&cfg.MCP, logger)

	// 测试 MCP 连接
	if err := app.mcpClient.Ping(ctx); err != nil {
		logger.Warnf("MCP server connection failed: %v", err)
	} else {
		logger.Info("MCP server connection successful")
	}

	// 初始化规则引擎
	app.ruleEngine, err = rules.NewRuleEngine(&cfg.Rules, logger)
	if err != nil {
		return nil, fmt.Errorf("failed to create rule engine: %w", err)
	}

	// 初始化日志分析器
	app.analyzer = analyzer.NewLogAnalyzer(cfg, llmClient, logger)

	// 初始化 ReAct Agent
	app.agent = agent.NewReActAgent(cfg, llmClient, logger)

	// 注册工具
	app.agent.RegisterTool(agent.NewLogAnalyzerTool(app.analyzer, app.ruleEngine))
	app.agent.RegisterTool(agent.NewTraceRetrieverTool(app.mcpClient))
	app.agent.RegisterTool(agent.NewRuleCheckerTool(app.ruleEngine))
	app.agent.RegisterTool(agent.NewLogSearchTool(app.mcpClient))

	// 初始化日志监控器
	app.monitor, err = monitor.NewLogMonitor(&cfg.Monitor, logger)
	if err != nil {
		return nil, fmt.Errorf("failed to create log monitor: %w", err)
	}

	return app, nil
}

// Start 启动应用
func (a *App) Start(ctx context.Context) error {
	a.logger.Info("Starting log monitor...")
	
	// 启动日志监控
	if err := a.monitor.Start(ctx); err != nil {
		return fmt.Errorf("failed to start log monitor: %w", err)
	}

	// 启动事件处理协程
	go a.handleLogEvents(ctx)

	a.logger.Info("Application started successfully")
	return nil
}

// Stop 停止应用
func (a *App) Stop() {
	a.logger.Info("Stopping log monitor...")
	a.monitor.Stop()
}

// handleLogEvents 处理日志事件
func (a *App) handleLogEvents(ctx context.Context) {
	for {
		select {
		case <-ctx.Done():
			return
		case event, ok := <-a.monitor.Events():
			if !ok {
				return
			}
			a.processLogEvent(ctx, event)
		}
	}
}

// processLogEvent 处理单个日志事件
func (a *App) processLogEvent(ctx context.Context, event monitor.LogEvent) {
	a.logger.Debugf("Processing log event: %s from %s", event.Type, event.File)

	// 只处理新增和修改的事件
	if event.Type != "new" && event.Type != "modified" {
		return
	}

	// 处理每个日志条目
	for _, entry := range event.Entries {
		if a.shouldProcessEntry(entry) {
			a.processLogEntry(ctx, entry)
		}
	}
}

// shouldProcessEntry 判断是否应该处理日志条目
func (a *App) shouldProcessEntry(entry monitor.LogEntry) bool {
	// 只处理错误和警告级别的日志
	return entry.Level == "ERROR" || entry.Level == "WARN"
}

// processLogEntry 处理单个日志条目
func (a *App) processLogEntry(ctx context.Context, entry monitor.LogEntry) {
	a.logger.Infof("Processing log entry: %s", entry.Message)

	// 构建问题描述
	question := fmt.Sprintf(`Analyze this log entry and provide recommendations:

Log Level: %s
Source: %s
Message: %s
Timestamp: %s`, 
		entry.Level, entry.Source, entry.Message, entry.Timestamp.Format(time.RFC3339))

	if entry.TraceID != "" {
		question += fmt.Sprintf("\nTrace ID: %s", entry.TraceID)
	}

	// 使用 ReAct Agent 分析
	result, err := a.agent.Run(ctx, question)
	if err != nil {
		a.logger.Errorf("Failed to analyze log entry: %v", err)
		return
	}

	// 输出分析结果
	a.outputAnalysisResult(entry, result)
}

// outputAnalysisResult 输出分析结果
func (a *App) outputAnalysisResult(entry monitor.LogEntry, result *agent.Result) {
	a.logger.Info("=== Log Analysis Result ===")
	a.logger.Infof("Log: %s", entry.Message)
	a.logger.Infof("Success: %t", result.Success)
	a.logger.Infof("Steps: %d", result.TotalSteps)
	a.logger.Infof("Duration: %v", result.Duration)

	if result.Success {
		a.logger.Infof("Answer: %s", result.Answer)
	} else {
		a.logger.Errorf("Error: %s", result.Error)
	}

	if a.config.Agent.Verbose {
		a.logger.Info("=== Detailed Steps ===")
		for _, step := range result.Steps {
			a.logger.Infof("Step %d:", step.StepNumber)
			a.logger.Infof("  Thought: %s", step.Thought)
			a.logger.Infof("  Action: %s", step.Action)
			a.logger.Infof("  Input: %s", step.ActionInput)
			a.logger.Infof("  Observation: %s", step.Observation)
		}
	}

	a.logger.Info("=== End Analysis ===")
}

// setupLogger 设置日志
func setupLogger(verbose bool) *logrus.Logger {
	logger := logrus.New()
	
	// 设置日志格式
	logger.SetFormatter(&logrus.TextFormatter{
		FullTimestamp: true,
		ForceColors:   true,
	})

	// 设置日志级别
	if verbose {
		logger.SetLevel(logrus.DebugLevel)
	} else {
		logger.SetLevel(logrus.InfoLevel)
	}

	return logger
}

// waitForSignal 等待信号
func waitForSignal(logger *logrus.Logger, cleanup func()) {
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	sig := <-sigChan
	logger.Infof("Received signal: %v", sig)
	cleanup()
}
