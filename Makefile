# ReAct Log Agent Makefile

.PHONY: build run test clean demo deps fmt lint

# 变量定义
APP_NAME := react-log-agent
BUILD_DIR := build
CONFIG_FILE := configs/config.yaml
TEST_CONFIG := examples/test_config.yaml

# 默认目标
all: deps fmt lint test build

# 安装依赖
deps:
	@echo "Installing dependencies..."
	go mod tidy
	go mod download

# 格式化代码
fmt:
	@echo "Formatting code..."
	go fmt ./...

# 代码检查
lint:
	@echo "Running linter..."
	@if command -v golangci-lint >/dev/null 2>&1; then \
		golangci-lint run; \
	else \
		echo "golangci-lint not installed, skipping..."; \
	fi

# 运行测试
test:
	@echo "Running tests..."
	go test -v ./...

# 构建应用
build:
	@echo "Building application..."
	@mkdir -p $(BUILD_DIR)
	go build -o $(BUILD_DIR)/$(APP_NAME) cmd/main.go

# 运行应用
run: build
	@echo "Running application..."
	./$(BUILD_DIR)/$(APP_NAME) -config $(CONFIG_FILE) -verbose

# 运行测试配置
run-test: build
	@echo "Running with test configuration..."
	./$(BUILD_DIR)/$(APP_NAME) -config $(TEST_CONFIG) -verbose

# 运行演示
demo:
	@echo "Running demo..."
	go run examples/demo.go

# 清理构建文件
clean:
	@echo "Cleaning build files..."
	rm -rf $(BUILD_DIR)

# 创建示例日志目录
setup-logs:
	@echo "Setting up log directories..."
	@mkdir -p examples/sample_logs
	@mkdir -p logs

# 检查配置
check-config:
	@echo "Checking configuration..."
	@if [ ! -f $(CONFIG_FILE) ]; then \
		echo "Config file not found: $(CONFIG_FILE)"; \
		exit 1; \
	fi
	@echo "Configuration file exists: $(CONFIG_FILE)"

# 显示帮助
help:
	@echo "Available targets:"
	@echo "  deps        - Install dependencies"
	@echo "  fmt         - Format code"
	@echo "  lint        - Run linter"
	@echo "  test        - Run tests"
	@echo "  build       - Build application"
	@echo "  run         - Run application with default config"
	@echo "  run-test    - Run application with test config"
	@echo "  demo        - Run demo program"
	@echo "  clean       - Clean build files"
	@echo "  setup-logs  - Create log directories"
	@echo "  check-config - Check configuration file"
	@echo "  help        - Show this help"

# 开发环境设置
dev-setup: deps setup-logs
	@echo "Development environment setup complete"
	@echo "Please set the following environment variables:"
	@echo "  export ARK_API_KEY='your_api_key'"
	@echo "  export ARK_MODEL='doubao-pro-32k'"
	@echo "  export REDIS_ADDR='localhost:6379'"
	@echo "  export MCP_SERVER_URL='http://localhost:8080'"

# 快速启动（用于开发）
quick-start: build setup-logs
	@echo "Quick starting with test configuration..."
	@echo "Make sure to set environment variables first!"
	./$(BUILD_DIR)/$(APP_NAME) -config $(TEST_CONFIG) -verbose
