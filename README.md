# ReAct Log Agent

基于 Eino 框架开发的 ReAct Agent 日志监控系统，具备智能日志分析和 MCP Server 集成能力。

## 功能特性

- 🔍 **实时日志监控** - 监控指定目录下的日志文件变化
- 🤖 **ReAct Agent** - 基于推理-行动循环的智能决策
- 🔗 **MCP Server 集成** - 调用 MCP Server 获取指定 traceID 的详细日志
- 📋 **规则引擎** - 基于预定义规则快速识别已知错误模式
- 🧠 **智能分析** - 使用大语言模型分析未知问题和异常
- 📊 **结构化输出** - 提供详细的分析报告和建议

## 项目结构

```
react-log-agent/
├── cmd/                    # 主程序入口
│   └── main.go
├── pkg/                    # 核心包
│   ├── agent/             # ReAct Agent 核心逻辑
│   ├── monitor/           # 日志监控模块
│   ├── mcp/               # MCP Server 客户端
│   ├── rules/             # 规则引擎
│   ├── analyzer/          # 智能分析模块
│   └── config/            # 配置管理
├── configs/               # 配置文件
├── examples/              # 示例和测试
└── docs/                  # 文档
```

## 快速开始

1. 安装依赖
```bash
go mod tidy
```

2. 配置环境变量
```bash
export ARK_API_KEY="your_api_key"
export ARK_MODEL="your_model_name"
export REDIS_ADDR="localhost:6379"
```

3. 运行程序
```bash
go run cmd/main.go
```

## 配置说明

详见 `configs/config.yaml` 配置文件。

## 开发指南

本项目基于字节开源的 Eino 框架开发，采用组件化架构设计。
