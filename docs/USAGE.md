# ReAct Log Agent 使用指南

## 概述

ReAct Log Agent 是一个基于 Eino 框架开发的智能日志监控和分析系统，采用 ReAct（Reasoning and Acting）模式，能够自动监控日志文件、识别问题并提供解决建议。

## 核心功能

### 1. 实时日志监控
- 监控指定目录下的日志文件变化
- 支持多种文件格式和模式匹配
- 实时检测新增和修改的日志条目

### 2. 规则引擎
- 基于预定义规则快速识别已知错误模式
- 支持正则表达式和通配符匹配
- 可配置的规则优先级和动作建议

### 3. 智能分析
- 使用大语言模型分析未知问题
- 提供详细的根因分析和影响评估
- 生成可执行的修复建议

### 4. MCP Server 集成
- 调用 MCP Server 获取详细的追踪信息
- 支持基于 TraceID 的日志检索
- 提供分布式系统的完整调用链分析

### 5. ReAct Agent
- 推理-行动循环的智能决策过程
- 多工具协作完成复杂分析任务
- 可追踪的分析步骤和决策过程

## 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd react-log-agent

# 安装依赖
make deps

# 设置环境变量
export ARK_API_KEY="your_api_key"
export ARK_MODEL="doubao-pro-32k"
export REDIS_ADDR="localhost:6379"
export MCP_SERVER_URL="http://localhost:8080"
```

### 2. 配置文件

复制并修改配置文件：

```bash
cp configs/config.yaml configs/config.local.yaml
# 编辑 configs/config.local.yaml 根据你的环境调整配置
```

### 3. 运行应用

```bash
# 构建并运行
make build
make run

# 或者使用测试配置
make run-test

# 运行演示程序
make demo
```

## 配置说明

### 日志监控配置

```yaml
monitor:
  log_dirs:
    - "/var/log/app"      # 监控的日志目录
    - "./logs"
  file_patterns:
    - "*.log"             # 文件模式匹配
    - "error*.log"
  interval: 5             # 监控间隔（秒）
  buffer_size: 1000       # 事件缓冲区大小
```

### MCP Server 配置

```yaml
mcp:
  server_url: "http://localhost:8080"
  auth:
    type: "bearer"        # 认证类型：bearer, basic, api_key
    token: "your_token"
  timeout: 30
  retry_count: 3
```

### LLM 配置

```yaml
llm:
  provider: "ark"         # 支持 ark, openai
  api_key: ""            # 从环境变量读取
  base_url: "https://ark.cn-beijing.volces.com/api/v3"
  model: "doubao-pro-32k"
  temperature: 0.1
  max_tokens: 4096
```

### Agent 配置

```yaml
agent:
  name: "LogAnalysisAgent"
  max_steps: 10           # 最大推理步数
  think_timeout: 30       # 思考超时时间
  verbose: true           # 详细日志
  tools:
    enabled:
      - "log_analyzer"
      - "trace_retriever"
      - "rule_checker"
```

## 使用示例

### 1. 基本日志分析

```go
// 创建分析请求
req := &analyzer.AnalysisRequest{
    LogText: "java.lang.OutOfMemoryError: Java heap space",
    Context: []string{},
}

// 执行分析
result, err := analyzer.AnalyzeLog(ctx, req)
if err != nil {
    log.Fatal(err)
}

fmt.Printf("分析结果: %s\n", result.Summary)
fmt.Printf("严重程度: %s\n", result.Severity)
```

### 2. 规则检查

```go
// 检查日志是否匹配规则
matches := ruleEngine.CheckLog(logText, context)

for _, match := range matches {
    fmt.Printf("匹配规则: %s\n", match.Rule.Name)
    fmt.Printf("建议动作: %v\n", match.Rule.Actions)
}
```

### 3. ReAct Agent 使用

```go
// 创建问题
question := "分析这个错误日志并提供解决方案: OutOfMemoryError"

// 运行 Agent
result, err := agent.Run(ctx, question)
if err != nil {
    log.Fatal(err)
}

fmt.Printf("分析结果: %s\n", result.Answer)
```

### 4. MCP Server 调用

```go
// 获取追踪信息
req := &mcp.TraceRequest{
    TraceID: "abc123def456",
}

trace, err := mcpClient.GetTrace(ctx, req)
if err != nil {
    log.Fatal(err)
}

fmt.Printf("追踪信息: %d spans, %d logs\n", 
    len(trace.Spans), len(trace.Logs))
```

## 规则配置

### 规则定义

```yaml
rules:
  - name: "OutOfMemoryError"
    description: "内存溢出错误"
    category: "memory"
    severity: "critical"
    patterns:
      - "OutOfMemoryError"
      - "java.lang.OutOfMemoryError"
    actions:
      - "restart_service"
      - "increase_memory"
    enabled: true
```

### 匹配配置

```yaml
matching:
  case_sensitive: false   # 是否区分大小写
  use_regex: true        # 是否使用正则表达式
  match_mode: "any"      # 匹配模式：any 或 all
  context_lines: 3       # 上下文行数
```

## 工具说明

### 1. log_analyzer
- 功能：分析日志条目，识别问题并提供建议
- 输入：日志文本
- 输出：详细的分析报告

### 2. trace_retriever
- 功能：从 MCP Server 获取追踪信息
- 输入：TraceID
- 输出：完整的追踪数据和相关日志

### 3. rule_checker
- 功能：检查日志是否匹配预定义规则
- 输入：日志文本
- 输出：匹配的规则和建议动作

### 4. log_search
- 功能：搜索符合条件的追踪和日志
- 输入：搜索条件（JSON格式）
- 输出：匹配的追踪列表

## 故障排除

### 常见问题

1. **LLM API 调用失败**
   - 检查 API Key 是否正确设置
   - 确认网络连接正常
   - 验证模型名称是否正确

2. **MCP Server 连接失败**
   - 检查 MCP Server 是否运行
   - 验证 URL 和认证信息
   - 查看网络防火墙设置

3. **日志监控不工作**
   - 确认日志目录存在且有读取权限
   - 检查文件模式匹配是否正确
   - 验证日志文件是否在更新

4. **规则不匹配**
   - 检查规则文件格式是否正确
   - 验证模式匹配配置
   - 确认规则是否启用

### 调试技巧

1. 启用详细日志：
```bash
./react-log-agent -config config.yaml -verbose
```

2. 使用演示程序测试：
```bash
make demo
```

3. 检查配置文件：
```bash
make check-config
```

## 扩展开发

### 添加新工具

1. 实现 `Tool` 接口：
```go
type CustomTool struct{}

func (t *CustomTool) Name() string {
    return "custom_tool"
}

func (t *CustomTool) Description() string {
    return "Custom tool description"
}

func (t *CustomTool) Execute(ctx context.Context, input string) (string, error) {
    // 实现工具逻辑
    return "result", nil
}
```

2. 注册工具：
```go
agent.RegisterTool(&CustomTool{})
```

### 添加新规则

编辑 `configs/rules.yaml` 文件：

```yaml
rules:
  - name: "CustomError"
    description: "自定义错误"
    category: "application"
    severity: "high"
    patterns:
      - "CustomException"
    actions:
      - "custom_action"
    enabled: true
```

### 自定义分析器

继承或包装现有的分析器：

```go
type CustomAnalyzer struct {
    *analyzer.LogAnalyzer
}

func (a *CustomAnalyzer) AnalyzeLog(ctx context.Context, req *analyzer.AnalysisRequest) (*analyzer.AnalysisResult, error) {
    // 自定义分析逻辑
    return a.LogAnalyzer.AnalyzeLog(ctx, req)
}
```

## 性能优化

### 1. 监控优化
- 调整监控间隔以平衡实时性和性能
- 使用合适的缓冲区大小
- 限制监控的文件数量和大小

### 2. LLM 调用优化
- 使用合适的模型参数
- 实现请求缓存机制
- 批量处理相似的日志条目

### 3. 规则引擎优化
- 优化规则匹配顺序
- 使用高效的正则表达式
- 缓存编译后的模式

## 最佳实践

1. **配置管理**
   - 使用环境变量管理敏感信息
   - 为不同环境创建不同的配置文件
   - 定期备份和版本控制配置

2. **日志管理**
   - 设置合适的日志级别
   - 定期清理旧日志文件
   - 监控日志文件大小

3. **安全考虑**
   - 保护 API Key 和认证信息
   - 限制文件访问权限
   - 使用 HTTPS 连接

4. **监控和告警**
   - 监控 Agent 运行状态
   - 设置关键错误的告警
   - 定期检查分析结果的准确性
