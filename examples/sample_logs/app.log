2024-01-15 10:30:15 INFO  [main] Application starting up
2024-01-15 10:30:16 INFO  [main] Database connection pool initialized
2024-01-15 10:30:17 INFO  [main] Server started on port 8080
2024-01-15 10:35:22 ERROR [http-thread-1] java.lang.OutOfMemoryError: Java heap space at com.example.service.UserService.processUsers(UserService.java:45) traceId=abc123def456
2024-01-15 10:35:23 ERROR [http-thread-1] Failed to process user request traceId=abc123def456
2024-01-15 10:40:10 WARN  [scheduler] Database connection timeout after 30 seconds
2024-01-15 10:40:11 ERROR [scheduler] SQLException: Connection refused to database server
2024-01-15 10:45:30 INFO  [http-thread-2] User login successful: user123
2024-01-15 10:50:15 ERROR [http-thread-3] NullPointerException in payment processing traceId=xyz789abc123
2024-01-15 10:55:20 WARN  [monitor] CPU usage at 95% for the last 5 minutes
2024-01-15 11:00:05 ERROR [disk-monitor] No space left on device: /var/log
2024-01-15 11:05:10 ERROR [auth-service] Authentication failed for user: admin traceId=def456ghi789
