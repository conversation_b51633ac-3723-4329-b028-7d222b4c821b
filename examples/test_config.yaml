# 测试配置文件

# 日志监控配置
monitor:
  log_dirs:
    - "./examples/sample_logs"
  file_patterns:
    - "*.log"
  interval: 2  # 更短的监控间隔用于测试
  buffer_size: 100

# MCP Server 配置（测试用模拟服务器）
mcp:
  server_url: "http://localhost:8080"
  auth:
    type: "bearer"
    token: "test-token"
  timeout: 10
  retry_count: 2

# 规则引擎配置
rules:
  rules_file: "./configs/rules.yaml"
  enabled: true
  priority: 1

# LLM 配置
llm:
  provider: "ark"
  api_key: ""  # 从环境变量读取
  base_url: "https://ark.cn-beijing.volces.com/api/v3"
  model: "doubao-pro-32k"
  temperature: 0.1
  max_tokens: 2048
  top_p: 0.9

# Redis 配置（测试环境）
redis:
  addr: "localhost:6379"
  password: ""
  db: 1  # 使用不同的数据库避免冲突
  pool_size: 5
  min_idle_conns: 2

# Agent 配置
agent:
  name: "TestLogAnalysisAgent"
  max_steps: 5  # 减少步数用于测试
  think_timeout: 15
  verbose: true
  tools:
    enabled:
      - "log_analyzer"
      - "rule_checker"
    log_analyzer:
      enabled: true
      config:
        max_lines: 100
    rule_checker:
      enabled: true
      config:
        strict_mode: false
