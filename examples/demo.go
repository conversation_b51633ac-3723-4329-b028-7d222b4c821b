package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/sirupsen/logrus"

	"react-log-agent/pkg/agent"
	"react-log-agent/pkg/analyzer"
	"react-log-agent/pkg/config"
	"react-log-agent/pkg/mcp"
	"react-log-agent/pkg/rules"
)

// 演示程序，展示如何使用 ReAct Agent 分析日志
func main() {
	// 设置日志
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)

	// 加载配置
	cfg, err := config.LoadConfig("examples/test_config.yaml")
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	ctx := context.Background()

	// 演示1: 规则引擎
	fmt.Println("=== Demo 1: Rule Engine ===")
	demoRuleEngine(cfg, logger)

	// 演示2: 日志分析器
	fmt.Println("\n=== Demo 2: Log Analyzer ===")
	demoLogAnalyzer(ctx, cfg, logger)

	// 演示3: ReAct Agent
	fmt.Println("\n=== Demo 3: ReAct Agent ===")
	demoReActAgent(ctx, cfg, logger)
}

func demoRuleEngine(cfg *config.Config, logger *logrus.Logger) {
	// 创建规则引擎
	ruleEngine, err := rules.NewRuleEngine(&cfg.Rules, logger)
	if err != nil {
		log.Printf("Failed to create rule engine: %v", err)
		return
	}

	// 测试日志
	testLogs := []string{
		"java.lang.OutOfMemoryError: Java heap space",
		"Connection refused to database server",
		"NullPointerException in payment processing",
		"CPU usage at 95% for the last 5 minutes",
		"No space left on device",
		"Authentication failed for user: admin",
	}

	for i, logText := range testLogs {
		fmt.Printf("\nTest %d: %s\n", i+1, logText)
		matches := ruleEngine.CheckLog(logText, []string{})
		
		if len(matches) > 0 {
			fmt.Printf("Matches found: %d\n", len(matches))
			for _, match := range matches {
				fmt.Printf("- Rule: %s (%s)\n", match.Rule.Name, match.Rule.Severity)
				fmt.Printf("  Description: %s\n", match.Rule.Description)
				fmt.Printf("  Actions: %v\n", match.Rule.Actions)
			}
		} else {
			fmt.Println("No rule matches found")
		}
	}
}

func demoLogAnalyzer(ctx context.Context, cfg *config.Config, logger *logrus.Logger) {
	// 检查是否有 LLM API Key
	if cfg.LLM.APIKey == "" {
		fmt.Println("LLM API key not configured, skipping log analyzer demo")
		return
	}

	// 创建 LLM 客户端
	llmClient, err := agent.NewEinoLLMClient(ctx, &cfg.LLM)
	if err != nil {
		log.Printf("Failed to create LLM client: %v", err)
		return
	}

	// 创建规则引擎
	ruleEngine, err := rules.NewRuleEngine(&cfg.Rules, logger)
	if err != nil {
		log.Printf("Failed to create rule engine: %v", err)
		return
	}

	// 创建分析器
	logAnalyzer := analyzer.NewLogAnalyzer(cfg, llmClient, logger)

	// 测试分析
	testLog := "java.lang.OutOfMemoryError: Java heap space at com.example.service.UserService.processUsers"
	
	// 检查规则匹配
	ruleMatches := ruleEngine.CheckLog(testLog, []string{})

	// 构建分析请求
	req := &analyzer.AnalysisRequest{
		LogText:     testLog,
		Context:     []string{},
		RuleMatches: ruleMatches,
	}

	fmt.Printf("Analyzing log: %s\n", testLog)
	
	result, err := logAnalyzer.AnalyzeLog(ctx, req)
	if err != nil {
		log.Printf("Failed to analyze log: %v", err)
		return
	}

	fmt.Printf("Analysis Result:\n")
	fmt.Printf("- Summary: %s\n", result.Summary)
	fmt.Printf("- Severity: %s\n", result.Severity)
	fmt.Printf("- Category: %s\n", result.Category)
	fmt.Printf("- Root Cause: %s\n", result.RootCause)
	fmt.Printf("- Confidence: %.2f\n", result.Confidence)
	fmt.Printf("- Analysis Time: %v\n", result.AnalysisTime)
}

func demoReActAgent(ctx context.Context, cfg *config.Config, logger *logrus.Logger) {
	// 检查是否有 LLM API Key
	if cfg.LLM.APIKey == "" {
		fmt.Println("LLM API key not configured, skipping ReAct agent demo")
		return
	}

	// 创建 LLM 客户端
	llmClient, err := agent.NewEinoLLMClient(ctx, &cfg.LLM)
	if err != nil {
		log.Printf("Failed to create LLM client: %v", err)
		return
	}

	// 创建组件
	ruleEngine, err := rules.NewRuleEngine(&cfg.Rules, logger)
	if err != nil {
		log.Printf("Failed to create rule engine: %v", err)
		return
	}

	logAnalyzer := analyzer.NewLogAnalyzer(cfg, llmClient, logger)
	mcpClient := mcp.NewMCPClient(&cfg.MCP, logger)

	// 创建 ReAct Agent
	reactAgent := agent.NewReActAgent(cfg, llmClient, logger)

	// 注册工具
	reactAgent.RegisterTool(agent.NewLogAnalyzerTool(logAnalyzer, ruleEngine))
	reactAgent.RegisterTool(agent.NewRuleCheckerTool(ruleEngine))
	reactAgent.RegisterTool(agent.NewTraceRetrieverTool(mcpClient))

	// 测试问题
	question := `I found this error in the logs: "java.lang.OutOfMemoryError: Java heap space at com.example.service.UserService.processUsers". 
What is the problem and what should I do to fix it?`

	fmt.Printf("Question: %s\n\n", question)
	fmt.Println("Running ReAct Agent...")

	// 运行 Agent
	result, err := reactAgent.Run(ctx, question)
	if err != nil {
		log.Printf("Failed to run ReAct agent: %v", err)
		return
	}

	// 输出结果
	fmt.Printf("\nReAct Agent Result:\n")
	fmt.Printf("Success: %t\n", result.Success)
	fmt.Printf("Total Steps: %d\n", result.TotalSteps)
	fmt.Printf("Duration: %v\n", result.Duration)

	if result.Success {
		fmt.Printf("Answer: %s\n", result.Answer)
	} else {
		fmt.Printf("Error: %s\n", result.Error)
	}

	// 显示详细步骤
	fmt.Println("\nDetailed Steps:")
	for _, step := range result.Steps {
		fmt.Printf("\nStep %d (%v):\n", step.StepNumber, step.Timestamp.Format(time.RFC3339))
		fmt.Printf("Thought: %s\n", step.Thought)
		fmt.Printf("Action: %s\n", step.Action)
		if step.ActionInput != "" {
			fmt.Printf("Action Input: %s\n", step.ActionInput)
		}
		if step.Observation != "" {
			fmt.Printf("Observation: %s\n", step.Observation)
		}
	}
}
