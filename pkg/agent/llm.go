package agent

import (
	"context"
	"fmt"

	"github.com/cloudwego/eino-ext/components/model/ark"
	"github.com/cloudwego/eino/components/model"
	"github.com/cloudwego/eino/schema"

	"react-log-agent/pkg/config"
)

// EinoLLMClient 基于 Eino 的 LLM 客户端
type EinoLLMClient struct {
	chatModel model.ChatModel
	config    *config.LLMConfig
}

// NewEinoLLMClient 创建新的 Eino LLM 客户端
func NewEinoLLMClient(ctx context.Context, cfg *config.LLMConfig) (*EinoLLMClient, error) {
	var chatModel model.ChatModel
	var err error

	switch cfg.Provider {
	case "ark":
		arkConfig := &ark.ChatModelConfig{
			BaseURL: cfg.BaseURL,
			APIKey:  cfg.APIKey,
			Model:   cfg.Model,
		}
		chatModel, err = ark.NewChatModel(ctx, arkConfig)
		if err != nil {
			return nil, fmt.<PERSON><PERSON><PERSON>("failed to create ark chat model: %w", err)
		}
	default:
		return nil, fmt.Errorf("unsupported LLM provider: %s", cfg.Provider)
	}

	return &EinoLLMClient{
		chatModel: chatModel,
		config:    cfg,
	}, nil
}

// Generate 生成文本
func (c *EinoLLMClient) Generate(ctx context.Context, prompt string) (string, error) {
	// 构建消息
	messages := []*schema.Message{
		{
			Role:    schema.User,
			Content: prompt,
		},
	}

	// 设置生成选项
	options := []model.ChatModelOption{
		model.WithTemperature(c.config.Temperature),
		model.WithMaxTokens(c.config.MaxTokens),
		model.WithTopP(c.config.TopP),
	}

	// 调用模型
	response, err := c.chatModel.Generate(ctx, messages, options...)
	if err != nil {
		return "", fmt.Errorf("failed to generate response: %w", err)
	}

	if response == nil || response.Content == "" {
		return "", fmt.Errorf("empty response from LLM")
	}

	return response.Content, nil
}

// GenerateStream 流式生成文本
func (c *EinoLLMClient) GenerateStream(ctx context.Context, prompt string) (*schema.StreamReader[*schema.Message], error) {
	messages := []*schema.Message{
		{
			Role:    schema.User,
			Content: prompt,
		},
	}

	options := []model.ChatModelOption{
		model.WithTemperature(c.config.Temperature),
		model.WithMaxTokens(c.config.MaxTokens),
		model.WithTopP(c.config.TopP),
	}

	return c.chatModel.Stream(ctx, messages, options...)
}
