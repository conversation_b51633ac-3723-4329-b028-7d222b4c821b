package agent

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"react-log-agent/pkg/analyzer"
	"react-log-agent/pkg/mcp"
	"react-log-agent/pkg/rules"
)

// LogAnalyzerTool 日志分析工具
type LogAnalyzerTool struct {
	analyzer *analyzer.LogAnalyzer
	rules    *rules.RuleEngine
}

// NewLogAnalyzerTool 创建日志分析工具
func NewLogAnalyzerTool(analyzer *analyzer.LogAnalyzer, rules *rules.RuleEngine) *LogAnalyzerTool {
	return &LogAnalyzerTool{
		analyzer: analyzer,
		rules:    rules,
	}
}

func (t *LogAnalyzerTool) Name() string {
	return "log_analyzer"
}

func (t *LogAnalyzerTool) Description() string {
	return "Analyze log entries to identify problems, determine severity, and provide recommendations. Input should be the log text to analyze."
}

func (t *LogAnalyzerTool) Execute(ctx context.Context, input string) (string, error) {
	// 首先检查规则匹配
	ruleMatches := t.rules.CheckLog(input, []string{})

	// 构建分析请求
	req := &analyzer.AnalysisRequest{
		LogText:     input,
		Context:     []string{},
		RuleMatches: ruleMatches,
	}

	// 执行分析
	result, err := t.analyzer.AnalyzeLog(ctx, req)
	if err != nil {
		return "", fmt.Errorf("failed to analyze log: %w", err)
	}

	// 格式化结果
	output := fmt.Sprintf(`Log Analysis Result:

Summary: %s
Severity: %s
Category: %s
Problem Type: %s
Root Cause: %s
Impact: %s
Confidence: %.2f

Recommendations:`, 
		result.Summary, result.Severity, result.Category, 
		result.ProblemType, result.RootCause, result.Impact, result.Confidence)

	for i, rec := range result.Recommendations {
		output += fmt.Sprintf("\n%d. [%s] %s - %s", i+1, rec.Priority, rec.Action, rec.Description)
		if rec.Command != "" {
			output += fmt.Sprintf("\n   Command: %s", rec.Command)
		}
	}

	if len(ruleMatches) > 0 {
		output += "\n\nRule Matches:\n"
		output += t.rules.GetMatchSummary(ruleMatches)
	}

	return output, nil
}

// TraceRetrieverTool 追踪检索工具
type TraceRetrieverTool struct {
	mcpClient *mcp.MCPClient
}

// NewTraceRetrieverTool 创建追踪检索工具
func NewTraceRetrieverTool(mcpClient *mcp.MCPClient) *TraceRetrieverTool {
	return &TraceRetrieverTool{
		mcpClient: mcpClient,
	}
}

func (t *TraceRetrieverTool) Name() string {
	return "trace_retriever"
}

func (t *TraceRetrieverTool) Description() string {
	return "Retrieve detailed trace information and logs for a specific trace ID from MCP server. Input should be the trace ID."
}

func (t *TraceRetrieverTool) Execute(ctx context.Context, input string) (string, error) {
	traceID := strings.TrimSpace(input)
	if traceID == "" {
		return "", fmt.Errorf("trace ID is required")
	}

	// 构建追踪请求
	req := &mcp.TraceRequest{
		TraceID: traceID,
	}

	// 获取追踪信息
	trace, err := t.mcpClient.GetTrace(ctx, req)
	if err != nil {
		return "", fmt.Errorf("failed to retrieve trace: %w", err)
	}

	// 格式化结果
	output := fmt.Sprintf(`Trace Information for ID: %s

Metadata:
- Total Spans: %d
- Total Logs: %d
- Duration: %v
- Services: %s
- Error Count: %d

Spans:`,
		traceID, trace.Metadata.TotalSpans, trace.Metadata.TotalLogs,
		trace.Metadata.Duration, strings.Join(trace.Metadata.ServiceNames, ", "),
		trace.Metadata.ErrorCount)

	for i, span := range trace.Spans {
		if i >= 5 { // 限制显示前5个span
			output += fmt.Sprintf("\n... and %d more spans", len(trace.Spans)-5)
			break
		}
		output += fmt.Sprintf("\n%d. %s (Duration: %v, Status: %d)",
			i+1, span.OperationName, span.Duration, span.Status.Code)
		if span.Status.Code != 0 {
			output += fmt.Sprintf(" - %s", span.Status.Message)
		}
	}

	output += "\n\nLogs:"
	for i, log := range trace.Logs {
		if i >= 10 { // 限制显示前10条日志
			output += fmt.Sprintf("\n... and %d more log entries", len(trace.Logs)-10)
			break
		}
		output += fmt.Sprintf("\n%d. [%s] %s: %s",
			i+1, log.Level, log.Service, log.Message)
	}

	return output, nil
}

// RuleCheckerTool 规则检查工具
type RuleCheckerTool struct {
	rules *rules.RuleEngine
}

// NewRuleCheckerTool 创建规则检查工具
func NewRuleCheckerTool(rules *rules.RuleEngine) *RuleCheckerTool {
	return &RuleCheckerTool{
		rules: rules,
	}
}

func (t *RuleCheckerTool) Name() string {
	return "rule_checker"
}

func (t *RuleCheckerTool) Description() string {
	return "Check log text against predefined rules to identify known error patterns. Input should be the log text to check."
}

func (t *RuleCheckerTool) Execute(ctx context.Context, input string) (string, error) {
	matches := t.rules.CheckLog(input, []string{})

	if len(matches) == 0 {
		return "No rule matches found. This may be a new or unknown issue that requires further analysis.", nil
	}

	output := t.rules.GetMatchSummary(matches)
	
	// 添加详细信息
	output += "\n\nDetailed Rule Information:\n"
	for i, match := range matches {
		output += fmt.Sprintf("\n%d. Rule: %s\n", i+1, match.Rule.Name)
		output += fmt.Sprintf("   Category: %s\n", match.Rule.Category)
		output += fmt.Sprintf("   Severity: %s\n", match.Rule.Severity)
		output += fmt.Sprintf("   Description: %s\n", match.Rule.Description)
		output += fmt.Sprintf("   Matched Text: %s\n", match.MatchedText)
		output += fmt.Sprintf("   Confidence: %.2f\n", match.Confidence)
		
		if len(match.Rule.Actions) > 0 {
			output += fmt.Sprintf("   Recommended Actions:\n")
			for _, action := range match.Rule.Actions {
				output += fmt.Sprintf("   - %s\n", action)
			}
		}
	}

	return output, nil
}

// LogSearchTool 日志搜索工具
type LogSearchTool struct {
	mcpClient *mcp.MCPClient
}

// NewLogSearchTool 创建日志搜索工具
func NewLogSearchTool(mcpClient *mcp.MCPClient) *LogSearchTool {
	return &LogSearchTool{
		mcpClient: mcpClient,
	}
}

func (t *LogSearchTool) Name() string {
	return "log_search"
}

func (t *LogSearchTool) Description() string {
	return "Search for traces and logs based on filters. Input should be JSON with search criteria like {\"service\": \"api\", \"level\": \"ERROR\"}."
}

func (t *LogSearchTool) Execute(ctx context.Context, input string) (string, error) {
	// 解析搜索条件
	var filters map[string]string
	if err := json.Unmarshal([]byte(input), &filters); err != nil {
		return "", fmt.Errorf("invalid search criteria JSON: %w", err)
	}

	// 执行搜索
	traces, err := t.mcpClient.SearchTraces(ctx, filters, 10)
	if err != nil {
		return "", fmt.Errorf("failed to search traces: %w", err)
	}

	if len(traces) == 0 {
		return "No traces found matching the search criteria.", nil
	}

	// 格式化结果
	output := fmt.Sprintf("Found %d traces matching the criteria:\n", len(traces))

	for i, trace := range traces {
		output += fmt.Sprintf("\n%d. Trace ID: %s\n", i+1, trace.TraceID)
		output += fmt.Sprintf("   Spans: %d, Logs: %d, Duration: %v\n",
			trace.Metadata.TotalSpans, trace.Metadata.TotalLogs, trace.Metadata.Duration)
		output += fmt.Sprintf("   Services: %s\n", strings.Join(trace.Metadata.ServiceNames, ", "))
		
		if trace.Metadata.ErrorCount > 0 {
			output += fmt.Sprintf("   Errors: %d\n", trace.Metadata.ErrorCount)
		}

		// 显示一些关键日志
		if len(trace.Logs) > 0 {
			output += "   Key logs:\n"
			for j, log := range trace.Logs {
				if j >= 3 { // 只显示前3条
					break
				}
				output += fmt.Sprintf("   - [%s] %s\n", log.Level, log.Message)
			}
		}
	}

	return output, nil
}
