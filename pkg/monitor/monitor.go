package monitor

import (
	"bufio"
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/fsnotify/fsnotify"
	"github.com/sirupsen/logrus"

	"react-log-agent/pkg/config"
)

// LogEntry 日志条目
type LogEntry struct {
	Timestamp time.Time `json:"timestamp"`
	Level     string    `json:"level"`
	Message   string    `json:"message"`
	Source    string    `json:"source"`
	TraceID   string    `json:"trace_id,omitempty"`
	Raw       string    `json:"raw"`
}

// LogEvent 日志事件
type LogEvent struct {
	Type      string     `json:"type"`      // new, modified, deleted
	File      string     `json:"file"`
	Entries   []LogEntry `json:"entries"`
	Timestamp time.Time  `json:"timestamp"`
}

// LogMonitor 日志监控器
type LogMonitor struct {
	config     *config.MonitorConfig
	logger     *logrus.Logger
	watcher    *fsnotify.Watcher
	eventChan  chan LogEvent
	stopChan   chan struct{}
	wg         sync.WaitGroup
	fileStates map[string]*FileState
	mutex      sync.RWMutex
}

// FileState 文件状态
type FileState struct {
	Path     string
	Size     int64
	ModTime  time.Time
	Position int64
}

// NewLogMonitor 创建新的日志监控器
func NewLogMonitor(cfg *config.MonitorConfig, logger *logrus.Logger) (*LogMonitor, error) {
	watcher, err := fsnotify.NewWatcher()
	if err != nil {
		return nil, fmt.Errorf("failed to create file watcher: %w", err)
	}

	monitor := &LogMonitor{
		config:     cfg,
		logger:     logger,
		watcher:    watcher,
		eventChan:  make(chan LogEvent, cfg.BufferSize),
		stopChan:   make(chan struct{}),
		fileStates: make(map[string]*FileState),
	}

	return monitor, nil
}

// Start 启动监控
func (m *LogMonitor) Start(ctx context.Context) error {
	// 添加监控目录
	for _, dir := range m.config.LogDirs {
		if err := m.addWatchDir(dir); err != nil {
			m.logger.Warnf("Failed to watch directory %s: %v", dir, err)
		}
	}

	// 启动文件监控协程
	m.wg.Add(1)
	go m.watchFiles(ctx)

	// 启动定期扫描协程
	m.wg.Add(1)
	go m.periodicScan(ctx)

	m.logger.Info("Log monitor started")
	return nil
}

// Stop 停止监控
func (m *LogMonitor) Stop() {
	close(m.stopChan)
	m.watcher.Close()
	m.wg.Wait()
	close(m.eventChan)
	m.logger.Info("Log monitor stopped")
}

// Events 获取事件通道
func (m *LogMonitor) Events() <-chan LogEvent {
	return m.eventChan
}

// addWatchDir 添加监控目录
func (m *LogMonitor) addWatchDir(dir string) error {
	// 检查目录是否存在
	if _, err := os.Stat(dir); os.IsNotExist(err) {
		return fmt.Errorf("directory does not exist: %s", dir)
	}

	// 添加目录到监控
	if err := m.watcher.Add(dir); err != nil {
		return fmt.Errorf("failed to add directory to watcher: %w", err)
	}

	// 扫描现有文件
	if err := m.scanDirectory(dir); err != nil {
		m.logger.Warnf("Failed to scan directory %s: %v", dir, err)
	}

	m.logger.Infof("Added watch directory: %s", dir)
	return nil
}

// scanDirectory 扫描目录中的日志文件
func (m *LogMonitor) scanDirectory(dir string) error {
	return filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if info.IsDir() {
			return nil
		}

		// 检查文件是否匹配模式
		if m.matchesPattern(info.Name()) {
			m.updateFileState(path, info)
		}

		return nil
	})
}

// matchesPattern 检查文件名是否匹配模式
func (m *LogMonitor) matchesPattern(filename string) bool {
	for _, pattern := range m.config.FilePatterns {
		if matched, _ := filepath.Match(pattern, filename); matched {
			return true
		}
	}
	return false
}

// updateFileState 更新文件状态
func (m *LogMonitor) updateFileState(path string, info os.FileInfo) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	oldState, exists := m.fileStates[path]
	newState := &FileState{
		Path:    path,
		Size:    info.Size(),
		ModTime: info.ModTime(),
	}

	if exists {
		newState.Position = oldState.Position
	}

	m.fileStates[path] = newState
}

// watchFiles 监控文件变化
func (m *LogMonitor) watchFiles(ctx context.Context) {
	defer m.wg.Done()

	for {
		select {
		case <-ctx.Done():
			return
		case <-m.stopChan:
			return
		case event, ok := <-m.watcher.Events:
			if !ok {
				return
			}
			m.handleFileEvent(event)
		case err, ok := <-m.watcher.Errors:
			if !ok {
				return
			}
			m.logger.Errorf("File watcher error: %v", err)
		}
	}
}

// handleFileEvent 处理文件事件
func (m *LogMonitor) handleFileEvent(event fsnotify.Event) {
	if !m.matchesPattern(filepath.Base(event.Name)) {
		return
	}

	switch {
	case event.Op&fsnotify.Write == fsnotify.Write:
		m.handleFileWrite(event.Name)
	case event.Op&fsnotify.Create == fsnotify.Create:
		m.handleFileCreate(event.Name)
	case event.Op&fsnotify.Remove == fsnotify.Remove:
		m.handleFileRemove(event.Name)
	}
}

// handleFileWrite 处理文件写入
func (m *LogMonitor) handleFileWrite(path string) {
	entries, err := m.readNewEntries(path)
	if err != nil {
		m.logger.Errorf("Failed to read new entries from %s: %v", path, err)
		return
	}

	if len(entries) > 0 {
		event := LogEvent{
			Type:      "modified",
			File:      path,
			Entries:   entries,
			Timestamp: time.Now(),
		}

		select {
		case m.eventChan <- event:
		default:
			m.logger.Warn("Event channel is full, dropping event")
		}
	}
}

// handleFileCreate 处理文件创建
func (m *LogMonitor) handleFileCreate(path string) {
	info, err := os.Stat(path)
	if err != nil {
		m.logger.Errorf("Failed to stat new file %s: %v", path, err)
		return
	}

	m.updateFileState(path, info)

	event := LogEvent{
		Type:      "new",
		File:      path,
		Entries:   []LogEntry{},
		Timestamp: time.Now(),
	}

	select {
	case m.eventChan <- event:
	default:
		m.logger.Warn("Event channel is full, dropping event")
	}
}

// handleFileRemove 处理文件删除
func (m *LogMonitor) handleFileRemove(path string) {
	m.mutex.Lock()
	delete(m.fileStates, path)
	m.mutex.Unlock()

	event := LogEvent{
		Type:      "deleted",
		File:      path,
		Entries:   []LogEntry{},
		Timestamp: time.Now(),
	}

	select {
	case m.eventChan <- event:
	default:
		m.logger.Warn("Event channel is full, dropping event")
	}
}

// readNewEntries 读取新的日志条目
func (m *LogMonitor) readNewEntries(path string) ([]LogEntry, error) {
	m.mutex.Lock()
	state, exists := m.fileStates[path]
	if !exists {
		m.mutex.Unlock()
		return nil, fmt.Errorf("file state not found: %s", path)
	}
	m.mutex.Unlock()

	file, err := os.Open(path)
	if err != nil {
		return nil, fmt.Errorf("failed to open file: %w", err)
	}
	defer file.Close()

	// 获取当前文件信息
	info, err := file.Stat()
	if err != nil {
		return nil, fmt.Errorf("failed to stat file: %w", err)
	}

	// 如果文件被截断，重置位置
	if info.Size() < state.Position {
		state.Position = 0
	}

	// 移动到上次读取位置
	if _, err := file.Seek(state.Position, 0); err != nil {
		return nil, fmt.Errorf("failed to seek file: %w", err)
	}

	var entries []LogEntry
	scanner := bufio.NewScanner(file)
	
	for scanner.Scan() {
		line := scanner.Text()
		if strings.TrimSpace(line) == "" {
			continue
		}

		entry := m.parseLogEntry(line, path)
		entries = append(entries, entry)
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("failed to scan file: %w", err)
	}

	// 更新文件位置
	currentPos, _ := file.Seek(0, 1)
	m.mutex.Lock()
	state.Position = currentPos
	state.Size = info.Size()
	state.ModTime = info.ModTime()
	m.mutex.Unlock()

	return entries, nil
}

// parseLogEntry 解析日志条目
func (m *LogMonitor) parseLogEntry(line, source string) LogEntry {
	entry := LogEntry{
		Timestamp: time.Now(),
		Source:    source,
		Raw:       line,
		Message:   line,
	}

	// 简单的日志解析逻辑
	// 可以根据实际日志格式进行扩展
	if strings.Contains(line, "ERROR") {
		entry.Level = "ERROR"
	} else if strings.Contains(line, "WARN") {
		entry.Level = "WARN"
	} else if strings.Contains(line, "INFO") {
		entry.Level = "INFO"
	} else if strings.Contains(line, "DEBUG") {
		entry.Level = "DEBUG"
	} else {
		entry.Level = "UNKNOWN"
	}

	// 尝试提取 TraceID
	if traceID := extractTraceID(line); traceID != "" {
		entry.TraceID = traceID
	}

	return entry
}

// extractTraceID 提取 TraceID
func extractTraceID(line string) string {
	// 常见的 TraceID 模式
	patterns := []string{
		"traceId=",
		"trace_id=",
		"trace-id=",
		"TraceID:",
		"[trace:",
	}

	for _, pattern := range patterns {
		if idx := strings.Index(line, pattern); idx != -1 {
			start := idx + len(pattern)
			end := start
			
			// 查找 TraceID 的结束位置
			for end < len(line) && (line[end] != ' ' && line[end] != ']' && line[end] != ',' && line[end] != '\n') {
				end++
			}
			
			if end > start {
				return line[start:end]
			}
		}
	}

	return ""
}

// periodicScan 定期扫描
func (m *LogMonitor) periodicScan(ctx context.Context) {
	defer m.wg.Done()

	ticker := time.NewTicker(time.Duration(m.config.Interval) * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-m.stopChan:
			return
		case <-ticker.C:
			m.performPeriodicScan()
		}
	}
}

// performPeriodicScan 执行定期扫描
func (m *LogMonitor) performPeriodicScan() {
	for _, dir := range m.config.LogDirs {
		if err := m.scanDirectory(dir); err != nil {
			m.logger.Errorf("Failed to scan directory %s: %v", dir, err)
		}
	}
}
