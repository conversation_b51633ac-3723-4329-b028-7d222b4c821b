package rules

import (
	"fmt"
	"os"
	"regexp"
	"strings"

	"github.com/sirupsen/logrus"
	"gopkg.in/yaml.v3"

	"react-log-agent/pkg/config"
)

// RuleEngine 规则引擎
type RuleEngine struct {
	config *config.RulesConfig
	rules  []Rule
	logger *logrus.Logger
}

// Rule 规则定义
type Rule struct {
	Name        string   `yaml:"name"`
	Description string   `yaml:"description"`
	Category    string   `yaml:"category"`
	Severity    string   `yaml:"severity"`
	Patterns    []string `yaml:"patterns"`
	Actions     []string `yaml:"actions"`
	Enabled     bool     `yaml:"enabled"`
}

// RulesConfig 规则配置文件结构
type RulesConfig struct {
	Rules    []Rule           `yaml:"rules"`
	Matching MatchingConfig   `yaml:"matching"`
	Actions  map[string]Action `yaml:"actions"`
}

// MatchingConfig 匹配配置
type MatchingConfig struct {
	CaseSensitive bool   `yaml:"case_sensitive"`
	UseRegex      bool   `yaml:"use_regex"`
	MatchMode     string `yaml:"match_mode"` // any, all
	ContextLines  int    `yaml:"context_lines"`
}

// Action 动作定义
type Action struct {
	Description string `yaml:"description"`
	Command     string `yaml:"command"`
}

// MatchResult 匹配结果
type MatchResult struct {
	Rule        Rule     `yaml:"rule"`
	Matched     bool     `yaml:"matched"`
	MatchedText string   `yaml:"matched_text"`
	Context     []string `yaml:"context"`
	Confidence  float64  `yaml:"confidence"`
}

// NewRuleEngine 创建新的规则引擎
func NewRuleEngine(cfg *config.RulesConfig, logger *logrus.Logger) (*RuleEngine, error) {
	engine := &RuleEngine{
		config: cfg,
		logger: logger,
	}

	if cfg.Enabled {
		if err := engine.loadRules(); err != nil {
			return nil, fmt.Errorf("failed to load rules: %w", err)
		}
	}

	return engine, nil
}

// loadRules 加载规则
func (e *RuleEngine) loadRules() error {
	if e.config.RulesFile == "" {
		return fmt.Errorf("rules file not specified")
	}

	data, err := os.ReadFile(e.config.RulesFile)
	if err != nil {
		return fmt.Errorf("failed to read rules file: %w", err)
	}

	var rulesConfig RulesConfig
	if err := yaml.Unmarshal(data, &rulesConfig); err != nil {
		return fmt.Errorf("failed to unmarshal rules: %w", err)
	}

	// 过滤启用的规则
	e.rules = make([]Rule, 0)
	for _, rule := range rulesConfig.Rules {
		if rule.Enabled {
			e.rules = append(e.rules, rule)
		}
	}

	e.logger.Infof("Loaded %d rules from %s", len(e.rules), e.config.RulesFile)
	return nil
}

// CheckLog 检查日志是否匹配规则
func (e *RuleEngine) CheckLog(logText string, context []string) []MatchResult {
	if !e.config.Enabled {
		return nil
	}

	var results []MatchResult

	for _, rule := range e.rules {
		result := e.checkRule(rule, logText, context)
		if result.Matched {
			results = append(results, result)
		}
	}

	return results
}

// checkRule 检查单个规则
func (e *RuleEngine) checkRule(rule Rule, logText string, context []string) MatchResult {
	result := MatchResult{
		Rule:    rule,
		Matched: false,
		Context: context,
	}

	matchCount := 0
	var matchedTexts []string

	for _, pattern := range rule.Patterns {
		matched, matchedText := e.matchPattern(pattern, logText)
		if matched {
			matchCount++
			matchedTexts = append(matchedTexts, matchedText)
		}
	}

	// 根据匹配模式判断是否匹配
	totalPatterns := len(rule.Patterns)
	if totalPatterns == 0 {
		return result
	}

	// 默认为 any 模式
	if matchCount > 0 {
		result.Matched = true
		result.MatchedText = strings.Join(matchedTexts, "; ")
		result.Confidence = float64(matchCount) / float64(totalPatterns)
	}

	return result
}

// matchPattern 匹配模式
func (e *RuleEngine) matchPattern(pattern, text string) (bool, string) {
	// 处理大小写敏感性
	searchText := text
	searchPattern := pattern
	
	// 默认不区分大小写
	searchText = strings.ToLower(searchText)
	searchPattern = strings.ToLower(searchPattern)

	// 使用正则表达式匹配
	if strings.Contains(pattern, "*") || strings.Contains(pattern, "[") || strings.Contains(pattern, "\\") {
		// 将简单通配符转换为正则表达式
		regexPattern := strings.ReplaceAll(searchPattern, "*", ".*")
		
		re, err := regexp.Compile(regexPattern)
		if err != nil {
			e.logger.Warnf("Invalid regex pattern: %s, error: %v", pattern, err)
			// 回退到简单字符串匹配
			if strings.Contains(searchText, searchPattern) {
				return true, pattern
			}
			return false, ""
		}

		matches := re.FindStringSubmatch(searchText)
		if len(matches) > 0 {
			return true, matches[0]
		}
	} else {
		// 简单字符串匹配
		if strings.Contains(searchText, searchPattern) {
			return true, pattern
		}
	}

	return false, ""
}

// GetRuleByName 根据名称获取规则
func (e *RuleEngine) GetRuleByName(name string) (*Rule, error) {
	for _, rule := range e.rules {
		if rule.Name == name {
			return &rule, nil
		}
	}
	return nil, fmt.Errorf("rule not found: %s", name)
}

// GetRulesByCategory 根据类别获取规则
func (e *RuleEngine) GetRulesByCategory(category string) []Rule {
	var rules []Rule
	for _, rule := range e.rules {
		if rule.Category == category {
			rules = append(rules, rule)
		}
	}
	return rules
}

// GetRulesBySeverity 根据严重程度获取规则
func (e *RuleEngine) GetRulesBySeverity(severity string) []Rule {
	var rules []Rule
	for _, rule := range e.rules {
		if rule.Severity == severity {
			rules = append(rules, rule)
		}
	}
	return rules
}

// GetAllRules 获取所有规则
func (e *RuleEngine) GetAllRules() []Rule {
	return e.rules
}

// ReloadRules 重新加载规则
func (e *RuleEngine) ReloadRules() error {
	return e.loadRules()
}

// AddRule 添加规则
func (e *RuleEngine) AddRule(rule Rule) {
	e.rules = append(e.rules, rule)
}

// RemoveRule 移除规则
func (e *RuleEngine) RemoveRule(name string) error {
	for i, rule := range e.rules {
		if rule.Name == name {
			e.rules = append(e.rules[:i], e.rules[i+1:]...)
			return nil
		}
	}
	return fmt.Errorf("rule not found: %s", name)
}

// GetMatchSummary 获取匹配摘要
func (e *RuleEngine) GetMatchSummary(results []MatchResult) string {
	if len(results) == 0 {
		return "No rules matched"
	}

	var summary strings.Builder
	summary.WriteString(fmt.Sprintf("Found %d rule matches:\n", len(results)))

	for i, result := range results {
		summary.WriteString(fmt.Sprintf("%d. %s (%s) - %s\n", 
			i+1, result.Rule.Name, result.Rule.Severity, result.Rule.Description))
		summary.WriteString(fmt.Sprintf("   Matched: %s\n", result.MatchedText))
		if len(result.Rule.Actions) > 0 {
			summary.WriteString(fmt.Sprintf("   Suggested actions: %s\n", 
				strings.Join(result.Rule.Actions, ", ")))
		}
	}

	return summary.String()
}
