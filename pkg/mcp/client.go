package mcp

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/sirupsen/logrus"

	"react-log-agent/pkg/config"
)

// MCPClient MCP Server 客户端
type MCPClient struct {
	config     *config.MCPConfig
	httpClient *http.Client
	logger     *logrus.Logger
}

// TraceRequest 追踪请求
type TraceRequest struct {
	TraceID     string            `json:"trace_id"`
	StartTime   *time.Time        `json:"start_time,omitempty"`
	EndTime     *time.Time        `json:"end_time,omitempty"`
	ServiceName string            `json:"service_name,omitempty"`
	Filters     map[string]string `json:"filters,omitempty"`
}

// TraceResponse 追踪响应
type TraceResponse struct {
	TraceID   string      `json:"trace_id"`
	Spans     []Span      `json:"spans"`
	Logs      []LogEntry  `json:"logs"`
	Metadata  TraceMetadata `json:"metadata"`
}

// Span 追踪跨度
type Span struct {
	SpanID       string            `json:"span_id"`
	TraceID      string            `json:"trace_id"`
	ParentSpanID string            `json:"parent_span_id,omitempty"`
	OperationName string           `json:"operation_name"`
	StartTime    time.Time         `json:"start_time"`
	EndTime      time.Time         `json:"end_time"`
	Duration     time.Duration     `json:"duration"`
	Tags         map[string]string `json:"tags"`
	Logs         []SpanLog         `json:"logs"`
	Status       SpanStatus        `json:"status"`
}

// SpanLog 跨度日志
type SpanLog struct {
	Timestamp time.Time         `json:"timestamp"`
	Fields    map[string]string `json:"fields"`
}

// SpanStatus 跨度状态
type SpanStatus struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// LogEntry 日志条目
type LogEntry struct {
	Timestamp time.Time         `json:"timestamp"`
	Level     string            `json:"level"`
	Message   string            `json:"message"`
	Service   string            `json:"service"`
	TraceID   string            `json:"trace_id"`
	SpanID    string            `json:"span_id,omitempty"`
	Fields    map[string]string `json:"fields"`
}

// TraceMetadata 追踪元数据
type TraceMetadata struct {
	TotalSpans   int           `json:"total_spans"`
	TotalLogs    int           `json:"total_logs"`
	Duration     time.Duration `json:"duration"`
	ServiceNames []string      `json:"service_names"`
	ErrorCount   int           `json:"error_count"`
}

// MCPError MCP 错误
type MCPError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

func (e *MCPError) Error() string {
	return fmt.Sprintf("MCP Error %d: %s", e.Code, e.Message)
}

// NewMCPClient 创建新的 MCP 客户端
func NewMCPClient(cfg *config.MCPConfig, logger *logrus.Logger) *MCPClient {
	return &MCPClient{
		config: cfg,
		httpClient: &http.Client{
			Timeout: time.Duration(cfg.Timeout) * time.Second,
		},
		logger: logger,
	}
}

// GetTrace 获取指定 TraceID 的追踪信息
func (c *MCPClient) GetTrace(ctx context.Context, req *TraceRequest) (*TraceResponse, error) {
	url := fmt.Sprintf("%s/api/v1/traces/%s", c.config.ServerURL, req.TraceID)
	
	// 构建请求体
	reqBody, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	// 创建 HTTP 请求
	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求头
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Accept", "application/json")

	// 添加认证信息
	if err := c.addAuth(httpReq); err != nil {
		return nil, fmt.Errorf("failed to add auth: %w", err)
	}

	// 执行请求（带重试）
	var resp *http.Response
	var lastErr error
	
	for attempt := 0; attempt <= c.config.RetryCount; attempt++ {
		if attempt > 0 {
			c.logger.Warnf("Retrying MCP request, attempt %d/%d", attempt, c.config.RetryCount)
			time.Sleep(time.Duration(attempt) * time.Second)
		}

		resp, lastErr = c.httpClient.Do(httpReq)
		if lastErr == nil && resp.StatusCode < 500 {
			break
		}
		
		if resp != nil {
			resp.Body.Close()
		}
	}

	if lastErr != nil {
		return nil, fmt.Errorf("failed to execute request after %d retries: %w", c.config.RetryCount, lastErr)
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	// 检查 HTTP 状态码
	if resp.StatusCode != http.StatusOK {
		var mcpErr MCPError
		if err := json.Unmarshal(body, &mcpErr); err != nil {
			return nil, fmt.Errorf("HTTP %d: %s", resp.StatusCode, string(body))
		}
		return nil, &mcpErr
	}

	// 解析响应
	var traceResp TraceResponse
	if err := json.Unmarshal(body, &traceResp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	c.logger.Infof("Successfully retrieved trace %s with %d spans and %d logs", 
		req.TraceID, len(traceResp.Spans), len(traceResp.Logs))

	return &traceResp, nil
}

// GetTraceLogs 获取指定 TraceID 的日志
func (c *MCPClient) GetTraceLogs(ctx context.Context, traceID string) ([]LogEntry, error) {
	req := &TraceRequest{
		TraceID: traceID,
	}

	resp, err := c.GetTrace(ctx, req)
	if err != nil {
		return nil, err
	}

	return resp.Logs, nil
}

// SearchTraces 搜索追踪
func (c *MCPClient) SearchTraces(ctx context.Context, filters map[string]string, limit int) ([]TraceResponse, error) {
	url := fmt.Sprintf("%s/api/v1/traces/search", c.config.ServerURL)
	
	searchReq := map[string]interface{}{
		"filters": filters,
		"limit":   limit,
	}

	reqBody, err := json.Marshal(searchReq)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal search request: %w", err)
	}

	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create search request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Accept", "application/json")

	if err := c.addAuth(httpReq); err != nil {
		return nil, fmt.Errorf("failed to add auth: %w", err)
	}

	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to execute search request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read search response: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("search request failed with status %d: %s", resp.StatusCode, string(body))
	}

	var traces []TraceResponse
	if err := json.Unmarshal(body, &traces); err != nil {
		return nil, fmt.Errorf("failed to unmarshal search response: %w", err)
	}

	return traces, nil
}

// addAuth 添加认证信息
func (c *MCPClient) addAuth(req *http.Request) error {
	switch c.config.Auth.Type {
	case "bearer":
		if c.config.Auth.Token == "" {
			return fmt.Errorf("bearer token is required")
		}
		req.Header.Set("Authorization", "Bearer "+c.config.Auth.Token)
	case "basic":
		if c.config.Auth.Username == "" || c.config.Auth.Password == "" {
			return fmt.Errorf("username and password are required for basic auth")
		}
		req.SetBasicAuth(c.config.Auth.Username, c.config.Auth.Password)
	case "api_key":
		if c.config.Auth.APIKey == "" {
			return fmt.Errorf("API key is required")
		}
		req.Header.Set("X-API-Key", c.config.Auth.APIKey)
	case "":
		// 无认证
	default:
		return fmt.Errorf("unsupported auth type: %s", c.config.Auth.Type)
	}
	return nil
}

// Ping 检查 MCP Server 连接
func (c *MCPClient) Ping(ctx context.Context) error {
	url := fmt.Sprintf("%s/api/v1/health", c.config.ServerURL)
	
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return fmt.Errorf("failed to create ping request: %w", err)
	}

	if err := c.addAuth(req); err != nil {
		return fmt.Errorf("failed to add auth: %w", err)
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to ping MCP server: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("MCP server health check failed with status %d", resp.StatusCode)
	}

	return nil
}
