package config

import (
	"fmt"
	"os"

	"gopkg.in/yaml.v3"
)

// Config 应用配置结构
type Config struct {
	// 日志监控配置
	Monitor MonitorConfig `yaml:"monitor"`
	
	// MCP Server 配置
	MCP MCPConfig `yaml:"mcp"`
	
	// 规则引擎配置
	Rules RulesConfig `yaml:"rules"`
	
	// LLM 配置
	LLM LLMConfig `yaml:"llm"`
	
	// Redis 配置
	Redis RedisConfig `yaml:"redis"`
	
	// Agent 配置
	Agent AgentConfig `yaml:"agent"`
}

// MonitorConfig 日志监控配置
type MonitorConfig struct {
	// 监控的日志目录
	LogDirs []string `yaml:"log_dirs"`
	
	// 日志文件模式匹配
	FilePatterns []string `yaml:"file_patterns"`
	
	// 监控间隔（秒）
	Interval int `yaml:"interval"`
	
	// 缓冲区大小
	BufferSize int `yaml:"buffer_size"`
}

// MCPConfig MCP Server 配置
type MCPConfig struct {
	// MCP Server 地址
	ServerURL string `yaml:"server_url"`
	
	// 认证信息
	Auth MCPAuth `yaml:"auth"`
	
	// 超时设置（秒）
	Timeout int `yaml:"timeout"`
	
	// 重试次数
	RetryCount int `yaml:"retry_count"`
}

// MCPAuth MCP 认证配置
type MCPAuth struct {
	Type     string `yaml:"type"`     // bearer, basic, api_key
	Token    string `yaml:"token"`
	Username string `yaml:"username"`
	Password string `yaml:"password"`
	APIKey   string `yaml:"api_key"`
}

// RulesConfig 规则引擎配置
type RulesConfig struct {
	// 规则文件路径
	RulesFile string `yaml:"rules_file"`
	
	// 是否启用规则引擎
	Enabled bool `yaml:"enabled"`
	
	// 规则优先级
	Priority int `yaml:"priority"`
}

// LLMConfig 大语言模型配置
type LLMConfig struct {
	// 模型提供商 (ark, openai, etc.)
	Provider string `yaml:"provider"`
	
	// API 配置
	APIKey  string `yaml:"api_key"`
	BaseURL string `yaml:"base_url"`
	Model   string `yaml:"model"`
	
	// 模型参数
	Temperature float64 `yaml:"temperature"`
	MaxTokens   int     `yaml:"max_tokens"`
	TopP        float64 `yaml:"top_p"`
}

// RedisConfig Redis 配置
type RedisConfig struct {
	Addr     string `yaml:"addr"`
	Password string `yaml:"password"`
	DB       int    `yaml:"db"`
	
	// 连接池配置
	PoolSize     int `yaml:"pool_size"`
	MinIdleConns int `yaml:"min_idle_conns"`
}

// AgentConfig Agent 配置
type AgentConfig struct {
	// Agent 名称
	Name string `yaml:"name"`
	
	// 最大推理步数
	MaxSteps int `yaml:"max_steps"`
	
	// 思考超时（秒）
	ThinkTimeout int `yaml:"think_timeout"`
	
	// 是否启用详细日志
	Verbose bool `yaml:"verbose"`
	
	// 工具配置
	Tools ToolsConfig `yaml:"tools"`
}

// ToolsConfig 工具配置
type ToolsConfig struct {
	// 启用的工具列表
	Enabled []string `yaml:"enabled"`
	
	// 工具特定配置
	LogAnalyzer   ToolConfig `yaml:"log_analyzer"`
	TraceRetriever ToolConfig `yaml:"trace_retriever"`
	RuleChecker   ToolConfig `yaml:"rule_checker"`
}

// ToolConfig 单个工具配置
type ToolConfig struct {
	Enabled bool                   `yaml:"enabled"`
	Config  map[string]interface{} `yaml:"config"`
}

// LoadConfig 从文件加载配置
func LoadConfig(configPath string) (*Config, error) {
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	// 从环境变量覆盖配置
	if err := config.loadFromEnv(); err != nil {
		return nil, fmt.Errorf("failed to load from env: %w", err)
	}

	return &config, nil
}

// loadFromEnv 从环境变量加载配置
func (c *Config) loadFromEnv() error {
	// LLM 配置
	if apiKey := os.Getenv("ARK_API_KEY"); apiKey != "" {
		c.LLM.APIKey = apiKey
	}
	if model := os.Getenv("ARK_MODEL"); model != "" {
		c.LLM.Model = model
	}
	if baseURL := os.Getenv("ARK_BASE_URL"); baseURL != "" {
		c.LLM.BaseURL = baseURL
	}

	// Redis 配置
	if addr := os.Getenv("REDIS_ADDR"); addr != "" {
		c.Redis.Addr = addr
	}
	if password := os.Getenv("REDIS_PASSWORD"); password != "" {
		c.Redis.Password = password
	}

	// MCP 配置
	if serverURL := os.Getenv("MCP_SERVER_URL"); serverURL != "" {
		c.MCP.ServerURL = serverURL
	}
	if token := os.Getenv("MCP_TOKEN"); token != "" {
		c.MCP.Auth.Token = token
	}

	return nil
}

// Validate 验证配置
func (c *Config) Validate() error {
	if c.LLM.APIKey == "" {
		return fmt.Errorf("LLM API key is required")
	}
	if c.LLM.Model == "" {
		return fmt.Errorf("LLM model is required")
	}
	if len(c.Monitor.LogDirs) == 0 {
		return fmt.Errorf("at least one log directory must be specified")
	}
	if c.MCP.ServerURL == "" {
		return fmt.Errorf("MCP server URL is required")
	}
	return nil
}
