# 日志分析规则配置

rules:
  # 错误规则
  - name: "OutOfMemoryError"
    description: "内存溢出错误"
    category: "memory"
    severity: "critical"
    patterns:
      - "OutOfMemoryError"
      - "java.lang.OutOfMemoryError"
      - "Cannot allocate memory"
    actions:
      - "restart_service"
      - "increase_memory"
    
  - name: "DatabaseConnectionError"
    description: "数据库连接错误"
    category: "database"
    severity: "high"
    patterns:
      - "Connection refused"
      - "Unable to connect to database"
      - "Connection timeout"
      - "SQLException"
    actions:
      - "check_database_status"
      - "restart_connection_pool"
    
  - name: "NullPointerException"
    description: "空指针异常"
    category: "application"
    severity: "medium"
    patterns:
      - "NullPointerException"
      - "null pointer"
      - "NPE"
    actions:
      - "check_code_logic"
      - "add_null_check"
    
  - name: "DiskSpaceError"
    description: "磁盘空间不足"
    category: "system"
    severity: "critical"
    patterns:
      - "No space left on device"
      - "Disk full"
      - "insufficient disk space"
    actions:
      - "clean_temp_files"
      - "expand_disk"
    
  - name: "NetworkTimeout"
    description: "网络超时"
    category: "network"
    severity: "medium"
    patterns:
      - "Connection timeout"
      - "Read timeout"
      - "SocketTimeoutException"
      - "Request timeout"
    actions:
      - "check_network_connectivity"
      - "increase_timeout"
    
  - name: "AuthenticationFailure"
    description: "认证失败"
    category: "security"
    severity: "high"
    patterns:
      - "Authentication failed"
      - "Invalid credentials"
      - "Unauthorized"
      - "Access denied"
    actions:
      - "check_credentials"
      - "review_permissions"
    
  - name: "HighCPUUsage"
    description: "CPU使用率过高"
    category: "performance"
    severity: "medium"
    patterns:
      - "CPU usage.*[89][0-9]%"
      - "CPU usage.*100%"
      - "High CPU load"
    actions:
      - "analyze_cpu_usage"
      - "optimize_performance"
    
  - name: "SlowQuery"
    description: "慢查询"
    category: "database"
    severity: "medium"
    patterns:
      - "Slow query"
      - "Query took.*[0-9]+s"
      - "Long running query"
    actions:
      - "optimize_query"
      - "add_index"

# 规则匹配配置
matching:
  # 是否区分大小写
  case_sensitive: false
  
  # 是否使用正则表达式
  use_regex: true
  
  # 匹配模式：any（任一匹配）或 all（全部匹配）
  match_mode: "any"
  
  # 上下文行数
  context_lines: 3

# 动作配置
actions:
  restart_service:
    description: "重启服务"
    command: "systemctl restart {service_name}"
    
  increase_memory:
    description: "增加内存配置"
    command: "echo 'Increase memory allocation'"
    
  check_database_status:
    description: "检查数据库状态"
    command: "systemctl status mysql"
    
  restart_connection_pool:
    description: "重启连接池"
    command: "echo 'Restart connection pool'"
    
  check_code_logic:
    description: "检查代码逻辑"
    command: "echo 'Review code for null pointer issues'"
    
  add_null_check:
    description: "添加空值检查"
    command: "echo 'Add null checks in code'"
    
  clean_temp_files:
    description: "清理临时文件"
    command: "find /tmp -type f -mtime +7 -delete"
    
  expand_disk:
    description: "扩展磁盘空间"
    command: "echo 'Expand disk space'"
    
  check_network_connectivity:
    description: "检查网络连接"
    command: "ping -c 4 *******"
    
  increase_timeout:
    description: "增加超时时间"
    command: "echo 'Increase timeout configuration'"
    
  check_credentials:
    description: "检查认证凭据"
    command: "echo 'Verify authentication credentials'"
    
  review_permissions:
    description: "检查权限配置"
    command: "echo 'Review user permissions'"
    
  analyze_cpu_usage:
    description: "分析CPU使用情况"
    command: "top -n 1"
    
  optimize_performance:
    description: "优化性能"
    command: "echo 'Optimize application performance'"
    
  optimize_query:
    description: "优化查询"
    command: "echo 'Optimize database query'"
    
  add_index:
    description: "添加数据库索引"
    command: "echo 'Add database index'"
