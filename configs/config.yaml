# ReAct Log Agent 配置文件

# 日志监控配置
monitor:
  log_dirs:
    - "/var/log/app"
    - "./logs"
  file_patterns:
    - "*.log"
    - "*.out"
    - "error*.log"
  interval: 5  # 监控间隔（秒）
  buffer_size: 1000

# MCP Server 配置
mcp:
  server_url: "http://localhost:8080"
  auth:
    type: "bearer"  # bearer, basic, api_key
    token: ""
  timeout: 30
  retry_count: 3

# 规则引擎配置
rules:
  rules_file: "./configs/rules.yaml"
  enabled: true
  priority: 1

# LLM 配置
llm:
  provider: "ark"  # ark, openai
  api_key: ""  # 从环境变量 ARK_API_KEY 读取
  base_url: "https://ark.cn-beijing.volces.com/api/v3"
  model: "doubao-pro-32k"
  temperature: 0.1
  max_tokens: 4096
  top_p: 0.9

# Redis 配置
redis:
  addr: "localhost:6379"
  password: ""
  db: 0
  pool_size: 10
  min_idle_conns: 5

# Agent 配置
agent:
  name: "LogAnalysisAgent"
  max_steps: 10
  think_timeout: 30
  verbose: true
  tools:
    enabled:
      - "log_analyzer"
      - "trace_retriever"
      - "rule_checker"
    log_analyzer:
      enabled: true
      config:
        max_lines: 1000
        context_lines: 5
    trace_retriever:
      enabled: true
      config:
        max_traces: 10
        include_context: true
    rule_checker:
      enabled: true
      config:
        strict_mode: false
